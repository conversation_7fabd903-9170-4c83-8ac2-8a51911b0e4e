<section class="py-12 lg:py-16" id="about-me">
  <div class="max-w-1300 xl:max-w-1250 mx-auto px-10 md:px-9 sm:px-8 xs:px-5">
    <div class="relative pb-0 lg:pb-25">
      <div class="text-4xl lg:text-5xl xl:text-6xl text-text-primary font-bold mb-5 font-pacifico">
        About <br> Roaa Ayman
      </div>

      <div class="flex justify-between gap-5 -mt-6 ml-5 sm:ml-10 xl:ml-32 2xl:ml-38 max-w-full
                  lg:flex-col lg:justify-center lg:items-center lg:ml-0 lg:gap-12 lg:-mt-2">

        <!-- Image Section -->
        <div class="relative w-80 h-80 lg:w-96 lg:h-96 sm:w-72 sm:h-72">
          <!-- Background pink div -->
          <div class="absolute bottom-0 -z-10 bg-accent-pink w-4/5 h-4/5"></div>

          <!-- Main image -->
          <div class="w-3/4 h-3/4 overflow-hidden absolute right-[15%] top-[15%]
                      shadow-pink transition-all duration-200 ease-out
                      hover:scale-150 hover:rounded-3xl hover:origin-top-center">
            <img src="images/roro3.jpg" alt="Roaa Ayman"
                 class="w-full h-full object-cover scale-200 origin-center transition-all duration-200 ease-out
                        hover:scale-100" />
          </div>
        </div>

        <!-- Content Section -->
        <div class="flex flex-col items-center gap-12 flex-none basis-2/5
                    lg:flex-row-reverse">

          <!-- Resume Button -->
          <a href="https://drive.google.com/file/d/1So0fhL4n2hvNY3CpCJktXAbe1ZYJg8be/view?usp=sharing"
             target="_blank" class="group">
            <button type="button" class="relative mx-auto py-3 px-4 transition-all duration-200 ease-out border-none bg-transparent cursor-pointer
                           before:content-[''] before:absolute before:top-0 before:left-0 before:block before:rounded-full
                           before:bg-primary-pink-lighter before:w-11 before:h-11 before:transition-all before:duration-300 before:ease-out
                           hover:before:w-full active:scale-95">
              <span class="relative font-ubuntu text-lg font-bold tracking-wider text-text-secondary">Resume</span>
              <svg width="15px" height="10px" viewBox="0 0 13 10"
                   class="relative top-0 ml-2 fill-none stroke-text-secondary stroke-2 stroke-round stroke-linejoin-round
                          -translate-x-1 transition-all duration-300 ease-out group-hover:translate-x-0">
                <path d="M1,5 L11,5"></path>
                <polyline points="8 1 12 5 8 9"></polyline>
              </svg>
            </button>
          </a>

          <!-- Description Text -->
          <div class="text-text-secondary font-semibold text-lg sm:text-base mt-5">
            {{ about.description || 'Loading description...' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
